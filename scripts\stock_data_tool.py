"""
通用股票数据爬取与规范化工具

设计目标
- 以 scripts/yf_quant_three_stocks.py 为参考，抽象出可复用的数据获取与指标计算逻辑
- 支持多输入（文本、截图OCR、混合）解析股票代码
- 支持多数据源（当前实现 Yahoo Finance，留有扩展接口）
- 输出结构化、标准化数据，兼容量化/股票分析提示词

依赖
- 必需: pandas, numpy
- 数据源: yfinance (可选但推荐)
- OCR(可选): pillow, pytesseract

用法参见文件末尾的示例。
"""
from __future__ import annotations

import re
import math
import json
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Iterable, Any

import numpy as np
import pandas as pd

try:
    import yfinance as yf  # 数据源适配器: Yahoo Finance
except Exception:  # pragma: no cover
    yf = None

try:
    from scipy import stats  # 蒙特卡洛模拟
except Exception:
    stats = None

try:
    from PIL import Image  # OCR 读取图片
    import pytesseract
except Exception:  # pragma: no cover
    Image = None
    pytesseract = None

# 股票名称映射表（常用A股）
STOCK_NAMES = {
    '601869.SH': '长飞光纤',
    '600522.SH': '中天科技',
    '600487.SH': '亨通光电',
    '000001.SZ': '平安银行',
    '000002.SZ': '万科A',
    '600036.SH': '招商银行',
    '600519.SH': '贵州茅台',
    '000858.SZ': '五粮液',
    '002415.SZ': '海康威视',
    '000300.SH': '沪深300ETF',
    # 可根据需要扩展
}

# 行业分类映射
INDUSTRY_MAP = {
    '601869.SH': '通信设备',
    '600522.SH': '通信设备',
    '600487.SH': '通信设备',
    '600036.SH': '银行',
    '600519.SH': '白酒',
    '000858.SZ': '白酒',
    '002415.SZ': '安防设备',
    # 可根据需要扩展
}


# ------------------------------ 常量配置 ---------------------------------
RF_DEFAULT = 0.02          # 年化无风险利率 (用于夏普)
WIN_3M = 63                # ~3个月交易日窗口
PERIOD_DEFAULT = "9mo"     # 拉取足够历史用于3m/60d
INTERVAL_DEFAULT = "1d"


# ------------------------------ 代码规范化 ---------------------------------
class CodeNormalizer:
    """将用户输入的代码规范化为统一的“规范代码(canonical)”与“数据源代码(provider)”

    规范代码示例:
    - A股: 600522.SH / 000001.SZ
    - 港股: 0700.HK
    - 美股: TSLA, AAPL
    - 指数: 000300.SH, ^GSPC
    
    Yahoo Finance 对应规则:
    - A股上交所: 600522.SS, 深交所: 000001.SZ
    - 沪深300: 000300.SS
    - 港股: 0700.HK
    - 美股: 代码原样
    - 指数: ^GSPC 等原样
    """

    A_SH_RE = re.compile(r"^(?:SH)?\s*([6]\d{5})(?:\.SH)?$", re.IGNORECASE)
    A_SZ_RE = re.compile(r"^(?:SZ)?\s*([03]\d{5})(?:\.SZ)?$", re.IGNORECASE)
    A_FULL_RE = re.compile(r"^(\d{6})\.(SH|SZ)$", re.IGNORECASE)
    HK_RE = re.compile(r"^(?:HK)?\s*(\d{4,5})(?:\.HK)?$", re.IGNORECASE)
    US_RE = re.compile(r"^[A-Z]{1,6}$", re.IGNORECASE)

    @classmethod
    def to_canonical(cls, raw: str) -> Optional[str]:
        s = raw.strip().upper()
        if not s:
            return None
        # 指数保留
        if s.startswith("^"):
            return s
        # 已带 .SH/.SZ
        m = cls.A_FULL_RE.match(s)
        if m:
            code, ex = m.group(1), m.group(2)
            return f"{code}.{ex}"
        # 推断上/深
        m = cls.A_SH_RE.match(s)
        if m:
            return f"{m.group(1)}.SH"
        m = cls.A_SZ_RE.match(s)
        if m:
            return f"{m.group(1)}.SZ"
        # 港股
        m = cls.HK_RE.match(s)
        if m:
            code = m.group(1).zfill(4)
            return f"{code}.HK"
        # 美股
        if cls.US_RE.match(s):
            return s
        return None

    @staticmethod
    def to_yahoo_ticker(canonical: str) -> str:
        # A股上交所: .SH -> .SS；深交所: .SZ 不变
        if canonical.startswith("^"):
            return canonical  # 指数原样
        if canonical.endswith(".SH"):
            return canonical.replace(".SH", ".SS")
        return canonical


# ------------------------------ OCR 解析 ---------------------------------
class OCRCodeExtractor:
    """从股票信息截图中提取股票代码和实时信息。依赖 pillow + pytesseract，可选安装。

    允许匹配:
    - 6位A股代码, 可伴随 SH/SZ
    - 港股4-5位数字
    - 美股英文代码
    - 含有各种分隔符、括号、中文文本等

    新增功能：
    - 提取当前价格、涨跌幅
    - 提取成交量信息
    - 识别异常涨跌（>5%）
    """

    CODE_PATTERN = re.compile(
        r"(\^?[A-Za-z]{1,6}|\d{6}(?:\.(?:SH|SZ))?|\d{4,5}(?:\.HK)?)"
    )

    # 价格模式：匹配价格数字
    PRICE_PATTERN = re.compile(r"(\d+\.?\d*)")

    # 涨跌幅模式：匹配 +10.00% 或 -5.23% 格式
    CHANGE_PATTERN = re.compile(r"([+-]?\d+\.?\d*%)")

    def __init__(self, lang: str = "eng") -> None:
        self.lang = lang

    def extract_codes(self, image_path: str) -> List[str]:
        if Image is None or pytesseract is None:
            raise RuntimeError("需要 pillow 和 pytesseract 才能进行截图OCR解析。")
        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, lang=self.lang)
        return self.extract_codes_from_text(text)

    def extract_full_info(self, image_path: str) -> Dict[str, Any]:
        """从截图中提取完整的股票信息，包括代码、价格、涨跌幅等"""
        if Image is None or pytesseract is None:
            raise RuntimeError("需要 pillow 和 pytesseract 才能进行截图OCR解析。")

        img = Image.open(image_path)
        text = pytesseract.image_to_string(img, lang=self.lang)

        # 提取股票代码
        codes = self.extract_codes_from_text(text)

        # 提取价格信息
        prices = self.PRICE_PATTERN.findall(text)

        # 提取涨跌幅信息
        changes = self.CHANGE_PATTERN.findall(text)

        return {
            'codes': codes,
            'prices': prices,
            'changes': changes,
            'raw_text': text,
            'anomaly_detected': any(abs(float(c.replace('%', '').replace('+', ''))) > 5
                                  for c in changes if c.replace('%', '').replace('+', '').replace('-', '').replace('.', '').isdigit())
        }

    @classmethod
    def extract_codes_from_text(cls, text: str) -> List[str]:
        if not text:
            return []
        found = cls.CODE_PATTERN.findall(text)
        # 过滤明显的非代码片段
        candidates = []
        for t in found:
            s = t.strip().upper()
            # 排除纯字母单字母噪声
            if re.fullmatch(r"[A-Z]", s):
                continue
            candidates.append(s)
        # 归一化
        canonicals = []
        for s in candidates:
            c = CodeNormalizer.to_canonical(s)
            if c:
                canonicals.append(c)
        # 去重
        out = []
        for c in canonicals:
            if c not in out:
                out.append(c)
        return out


# --------------------------- 数据源适配接口 -------------------------------
class DataSource:
    def fetch(self, canonical_codes: List[str], period: str, interval: str) -> Tuple[Dict[str, pd.DataFrame], List[str]]:
        """拉取多标的OHLCV数据
        返回: (frames, errors)
        - frames: {canonical_code: df}
          df列包含: open/high/low/close/volume
        - errors: 拉取失败的规范代码list
        """
        raise NotImplementedError


class YahooFinanceSource(DataSource):
    def __init__(self) -> None:
        if yf is None:
            raise RuntimeError("未安装 yfinance，无法使用 YahooFinance 数据源。请先 pip install yfinance")

    def fetch(self, canonical_codes: List[str], period: str, interval: str) -> Tuple[Dict[str, pd.DataFrame], List[str]]:
        if not canonical_codes:
            return {}, []
        mapping = {c: CodeNormalizer.to_yahoo_ticker(c) for c in canonical_codes}
        tickers = list(mapping.values())
        data = yf.download(tickers, period=period, interval=interval, auto_adjust=False, progress=False)

        frames: Dict[str, pd.DataFrame] = {}
        errors: List[str] = []

        def to_df(sym_provider: str) -> Optional[pd.DataFrame]:
            try:
                sub = {
                    'open': data['Open'][sym_provider],
                    'high': data['High'][sym_provider],
                    'low': data['Low'][sym_provider],
                    'close': data['Close'][sym_provider],
                    'volume': data['Volume'][sym_provider],
                }
                df = pd.DataFrame(sub).dropna()
                return df
            except Exception:
                return None

        for c, y in mapping.items():
            df = to_df(y)
            if df is None or df.empty:
                errors.append(c)
            else:
                df['ret'] = df['close'].pct_change()
                frames[c] = df
        return frames, errors


# ------------------------------ 指标计算 ---------------------------------

def rsi14(close: pd.Series) -> pd.Series:
    delta = close.diff()
    gain = delta.clip(lower=0).ewm(alpha=1/14, adjust=False).mean()
    loss = (-delta.clip(upper=0)).ewm(alpha=1/14, adjust=False).mean()
    rs = gain / loss.replace(0, np.nan)
    return 100 - 100 / (1 + rs)


def macd(close: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
    ema12 = close.ewm(span=12, adjust=False).mean()
    ema26 = close.ewm(span=26, adjust=False).mean()
    macd_val = ema12 - ema26
    signal = macd_val.ewm(span=9, adjust=False).mean()
    hist = macd_val - signal
    return macd_val, signal, hist


def atr14(df: pd.DataFrame) -> float:
    prev_close = df['close'].shift(1)
    tr = pd.concat([
        (df['high'] - df['low']).abs(),
        (df['high'] - prev_close).abs(),
        (df['low'] - prev_close).abs()
    ], axis=1).max(axis=1)
    return float(tr.rolling(14).mean().iloc[-1])


def support_resistance(close: pd.Series, win_3m: int = WIN_3M) -> Dict[str, float]:
    recent = close.tail(win_3m)
    q10, q50, q90 = recent.quantile([0.10, 0.50, 0.90])
    swing_high = recent.rolling(5, center=True).max().iloc[-10:].max()
    swing_low = recent.rolling(5, center=True).min().iloc[-10:].min()
    return {
        'q10': float(q10),
        'median': float(q50),
        'q90': float(q90),
        'swing_high': float(swing_high),
        'swing_low': float(swing_low),
    }


def get_stock_name(code: str) -> str:
    """获取股票中文名称"""
    return STOCK_NAMES.get(code, code)

def get_industry(code: str) -> str:
    """获取股票行业分类"""
    return INDUSTRY_MAP.get(code, "未分类")

def calculate_technical_score(rsi: float, macd_hist: float, bb_pos: float, ma_state: str = 'unknown') -> int:
    """基于技术指标计算技术面评分（1-5分）

    评分标准：
    5⭐：RSI(30-70) + MACD柱正值 + 布林带中轨 + 均线多头排列
    4⭐：3个指标正常
    3⭐：2个指标正常或全部中性
    2⭐：1个指标正常
    1⭐：多个指标异常
    """
    positive_indicators = 0

    # RSI评分：30-70正常区间
    if 30 <= rsi <= 70:
        positive_indicators += 1

    # MACD柱状图：正值为好
    if macd_hist > 0:
        positive_indicators += 1

    # 布林带位置：中轨附近为好
    if 0.2 <= bb_pos <= 0.8:
        positive_indicators += 1

    # 均线状态：多头排列为好
    if ma_state == 'bullish' or ma_state == 'golden':
        positive_indicators += 1

    # 根据正面指标数量评分
    if positive_indicators >= 4:
        return 5
    elif positive_indicators == 3:
        return 4
    elif positive_indicators == 2:
        return 3
    elif positive_indicators == 1:
        return 2
    else:
        return 1

def calculate_fund_score(vol_chg: float, beta: float) -> int:
    """基于成交量和贝塔值计算资金面评分（1-5分）"""
    score = 3  # 基础分

    # 成交量变化：放量+1分，缩量-1分
    if vol_chg > 0.5:  # 放量50%以上
        score += 1
    elif vol_chg < -0.3:  # 缩量30%以上
        score -= 1

    # 贝塔值：0.8-1.2正常+1分，过高过低-1分
    if 0.8 <= beta <= 1.2:
        score += 1
    elif beta > 2.0 or beta < 0.5:
        score -= 1

    return max(1, min(5, score))

def calculate_quant_score(sharpe: float, mdd: float, alpha: float) -> int:
    """基于夏普比率、最大回撤、阿尔法计算量化面评分（1-5分）

    评分标准：
    5⭐：夏普>1.5 + 最大回撤<10% + 阿尔法显著为正
    4⭐：夏普>1.0 + 回撤<15%
    3⭐：夏普>0.5 + 回撤<20%
    2⭐：夏普>0 + 回撤<30%
    1⭐：夏普<0 或回撤>30%
    """
    # 转换回撤为百分比（如果是小数）
    mdd_pct = mdd * 100 if mdd < 1 else mdd
    alpha_pct = alpha * 100 if abs(alpha) < 1 else alpha

    if sharpe > 1.5 and mdd_pct < 10 and alpha_pct > 5:
        return 5
    elif sharpe > 1.0 and mdd_pct < 15:
        return 4
    elif sharpe > 0.5 and mdd_pct < 20:
        return 3
    elif sharpe > 0 and mdd_pct < 30:
        return 2
    else:
        return 1

def get_overall_rating(tech_score: int, fund_score: int, quant_score: int, fundamental_score: int = 3) -> str:
    """基于四个维度评分计算综合评级"""
    total = tech_score + fund_score + quant_score + fundamental_score

    # 四维评分标准（满分20分）
    if total >= 17:  # 平均4.25分以上
        return "买入"
    elif total >= 13:  # 平均3.25分以上
        return "持有"
    elif total >= 9:   # 平均2.25分以上
        return "观望"
    elif total >= 6:   # 平均1.5分以上
        return "减持"
    else:
        return "卖出"

def calculate_position_size(overall_rating: str, risk_level: str) -> str:
    """基于综合评级和风险等级计算仓位建议"""
    if overall_rating == "买入":
        if risk_level == "低":
            return "20-25%"
        elif risk_level == "中":
            return "15-20%"
        else:
            return "10-15%"
    elif overall_rating == "持有":
        if risk_level == "低":
            return "15-20%"
        elif risk_level == "中":
            return "10-15%"
        else:
            return "5-10%"
    elif overall_rating == "观望":
        return "5%以下"
    else:
        return "0%"

def get_operation_advice(overall_rating: str, rsi: float, vol_chg: float) -> str:
    """基于综合评级和技术指标给出具体操作建议"""
    if overall_rating == "买入":
        if rsi > 80:
            return "等待回调后分批建仓"
        elif vol_chg > 0.5:
            return "分批建仓，关注放量持续性"
        else:
            return "可适度建仓"
    elif overall_rating == "持有":
        if rsi > 75:
            return "减仓观望，等待技术调整"
        else:
            return "持有为主，可适度加仓"
    elif overall_rating == "观望":
        return "暂时观望，等待更好时机"
    elif overall_rating == "减持":
        return "逐步减仓，控制风险"
    else:
        return "建议清仓，规避风险"

def monte_carlo_simulation(current_price: float, mu: float, sigma: float,
                          days: int = 20, simulations: int = 1000) -> Dict[str, float]:
    """蒙特卡洛模拟预测股价分布

    基于几何布朗运动模型：S(t) = S(0) * exp((μ - σ²/2)*t + σ*W(t))

    Args:
        current_price: 当前股价
        mu: 日均收益率
        sigma: 日波动率
        days: 预测天数
        simulations: 模拟次数

    Returns:
        包含P5, P25, P50, P75, P95分位数和期望值的字典
    """
    if not stats:
        # 如果没有scipy，使用简化版本
        return {
            'expected': current_price * (1 + mu * days),
            'p5': current_price * (1 + mu * days - 2 * sigma * math.sqrt(days)),
            'p25': current_price * (1 + mu * days - sigma * math.sqrt(days)),
            'p50': current_price * (1 + mu * days),
            'p75': current_price * (1 + mu * days + sigma * math.sqrt(days)),
            'p95': current_price * (1 + mu * days + 2 * sigma * math.sqrt(days))
        }

    # 设置随机种子确保结果可重现
    np.random.seed(42)

    # 几何布朗运动参数
    dt = 1  # 日频数据
    drift = mu - 0.5 * sigma**2

    # 运行蒙特卡洛模拟
    final_prices = []

    for _ in range(simulations):
        price = current_price
        for _ in range(days):
            # 几何布朗运动步进
            random_shock = np.random.normal(0, 1)
            price = price * math.exp(drift * dt + sigma * math.sqrt(dt) * random_shock)
        final_prices.append(price)

    final_prices = np.array(final_prices)

    return {
        'expected': float(np.mean(final_prices)),
        'p5': float(np.percentile(final_prices, 5)),
        'p25': float(np.percentile(final_prices, 25)),
        'p50': float(np.percentile(final_prices, 50)),
        'p75': float(np.percentile(final_prices, 75)),
        'p95': float(np.percentile(final_prices, 95)),
        'std': float(np.std(final_prices)),
        'simulations': simulations
    }

def enhanced_scenario_analysis(mc_result: Dict[str, float], current_price: float) -> Dict[str, Any]:
    """基于蒙特卡洛结果的增强情景分析"""
    if not mc_result:
        return {}

    # 计算收益率分位数
    p5_return = (mc_result['p5'] / current_price - 1) * 100
    p25_return = (mc_result['p25'] / current_price - 1) * 100
    p50_return = (mc_result['p50'] / current_price - 1) * 100
    p75_return = (mc_result['p75'] / current_price - 1) * 100
    p95_return = (mc_result['p95'] / current_price - 1) * 100

    return {
        'pessimistic': {
            'probability': 20,
            'return_pct': p5_return,
            'description': f"跌破支撑，跌幅{p5_return:.1f}%"
        },
        'conservative': {
            'probability': 30,
            'return_pct': p25_return,
            'description': f"谨慎整理，涨跌{p25_return:+.1f}%"
        },
        'base': {
            'probability': 30,
            'return_pct': p50_return,
            'description': f"区间震荡，涨跌{p50_return:+.1f}%"
        },
        'optimistic': {
            'probability': 20,
            'return_pct': p95_return,
            'description': f"突破阻力，涨幅{p95_return:+.1f}%"
        },
        'win_probability': float(np.sum(np.array([mc_result['p25'], mc_result['p50'], mc_result['p75'], mc_result['p95']]) > current_price) / 4 * 100)
    }

def get_fundamental_data(symbol: str) -> Dict[str, Any]:
    """获取基本面数据（PE、PB、ROE等）"""
    if not yf:
        return {}

    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info

        # 提取关键基本面指标
        fundamentals = {
            'pe_ratio': info.get('trailingPE'),
            'pb_ratio': info.get('priceToBook'),
            'roe': info.get('returnOnEquity'),
            'roa': info.get('returnOnAssets'),
            'profit_margin': info.get('profitMargins'),
            'revenue_growth': info.get('revenueGrowth'),
            'earnings_growth': info.get('earningsGrowth'),
            'debt_to_equity': info.get('debtToEquity'),
            'current_ratio': info.get('currentRatio'),
            'market_cap': info.get('marketCap'),
            'enterprise_value': info.get('enterpriseValue'),
            'ev_revenue': info.get('enterpriseToRevenue'),
            'ev_ebitda': info.get('enterpriseToEbitda'),
            'dividend_yield': info.get('dividendYield'),
            'peg_ratio': info.get('pegRatio'),
            'book_value': info.get('bookValue'),
            'price_to_sales': info.get('priceToSalesTrailing12Months'),
            'operating_margin': info.get('operatingMargins'),
            'gross_margin': info.get('grossMargins'),
            'beta': info.get('beta'),
            'sector': info.get('sector'),
            'industry': info.get('industry'),
            'business_summary': info.get('longBusinessSummary', '')[:200] + '...' if info.get('longBusinessSummary') else None
        }

        # 清理None值
        fundamentals = {k: v for k, v in fundamentals.items() if v is not None}

        return fundamentals

    except Exception as e:
        print(f"获取{symbol}基本面数据失败: {e}")
        return {}

def calculate_fundamental_score(fundamentals: Dict[str, Any]) -> int:
    """基于基本面数据计算基本面评分（1-5分）"""
    if not fundamentals:
        return 3  # 默认中性

    score = 3  # 基础分

    # PE估值评分
    pe = fundamentals.get('pe_ratio')
    if pe:
        if 10 <= pe <= 20:  # 合理估值
            score += 1
        elif pe > 50 or pe < 5:  # 极端估值
            score -= 1

    # ROE盈利能力评分
    roe = fundamentals.get('roe')
    if roe:
        if roe > 0.15:  # ROE > 15%
            score += 1
        elif roe < 0.05:  # ROE < 5%
            score -= 1

    # 营收增长评分
    revenue_growth = fundamentals.get('revenue_growth')
    if revenue_growth:
        if revenue_growth > 0.1:  # 营收增长 > 10%
            score += 1
        elif revenue_growth < -0.05:  # 营收下降 > 5%
            score -= 1

    # 债务水平评分
    debt_to_equity = fundamentals.get('debt_to_equity')
    if debt_to_equity:
        if debt_to_equity < 0.5:  # 低负债
            score += 1
        elif debt_to_equity > 2.0:  # 高负债
            score -= 1

    return max(1, min(5, score))

def industry_comparison_analysis(metrics_dict: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """行业对比分析"""
    if len(metrics_dict) < 2:
        return {}

    # 按行业分组
    industry_groups = {}
    for code, metrics in metrics_dict.items():
        industry = metrics.get('fundamentals', {}).get('industry', '未知行业')
        if industry not in industry_groups:
            industry_groups[industry] = []
        industry_groups[industry].append((code, metrics))

    # 计算行业内排名
    comparison_result = {
        'industry_groups': industry_groups,
        'rankings': {},
        'relative_analysis': {}
    }

    # 如果同行业股票数量>=2，进行详细对比
    for industry, stocks in industry_groups.items():
        if len(stocks) >= 2:
            # 按各项指标排名
            stocks_data = [(code, metrics) for code, metrics in stocks]

            # 收益率排名
            returns_ranking = sorted(stocks_data,
                                   key=lambda x: x[1].get('cumret_3m', 0),
                                   reverse=True)

            # 风险调整收益排名（夏普比率）
            sharpe_ranking = sorted(stocks_data,
                                  key=lambda x: x[1].get('sharpe', 0) or 0,
                                  reverse=True)

            # 估值排名（PE从低到高）
            pe_ranking = sorted([s for s in stocks_data if s[1].get('fundamentals', {}).get('pe_ratio')],
                              key=lambda x: x[1].get('fundamentals', {}).get('pe_ratio', 999))

            # 基本面综合排名
            fundamental_ranking = sorted(stocks_data,
                                       key=lambda x: x[1].get('fundamental_score', 3),
                                       reverse=True)

            comparison_result['rankings'][industry] = {
                'returns': [(code, f"{metrics.get('cumret_3m', 0)*100:+.1f}%") for code, metrics in returns_ranking],
                'sharpe': [(code, f"{metrics.get('sharpe', 0):.2f}") for code, metrics in sharpe_ranking],
                'valuation': [(code, f"PE {metrics.get('fundamentals', {}).get('pe_ratio', 0):.1f}") for code, metrics in pe_ranking],
                'fundamentals': [(code, f"{metrics.get('fundamental_score', 3)}⭐") for code, metrics in fundamental_ranking]
            }

            # 相对强弱分析
            avg_return = np.mean([m.get('cumret_3m', 0) for _, m in stocks_data])
            avg_sharpe = np.mean([m.get('sharpe', 0) or 0 for _, m in stocks_data])

            for code, metrics in stocks_data:
                rel_return = (metrics.get('cumret_3m', 0) - avg_return) * 100
                rel_sharpe = (metrics.get('sharpe', 0) or 0) - avg_sharpe

                comparison_result['relative_analysis'][code] = {
                    'relative_return': rel_return,
                    'relative_sharpe': rel_sharpe,
                    'industry_rank': next(i for i, (c, _) in enumerate(returns_ranking, 1) if c == code),
                    'total_in_industry': len(stocks_data)
                }

    return comparison_result

def calculate_target_price(current: float, support_resistance: dict, trend_strength: float) -> float:
    """基于支撑阻力和趋势强度计算目标价"""
    if not support_resistance:
        return current * 1.05  # 默认5%涨幅

    q90 = support_resistance.get('q90', current)
    swing_high = support_resistance.get('swing_high', current)

    # 根据趋势强度调整目标价
    if trend_strength > 1.0:  # 强势上涨
        return max(q90 * 1.1, swing_high * 1.05)
    elif trend_strength > 0.5:  # 温和上涨
        return max(q90, swing_high * 1.02)
    else:  # 震荡或下跌
        return min(q90 * 0.95, current * 1.02)

def compute_metrics(frames: Dict[str, pd.DataFrame],
                    benchmark: Optional[str] = None,
                    rf: float = RF_DEFAULT,
                    win_3m: int = WIN_3M) -> Dict[str, Dict[str, Any]]:
    """对给定标的集合计算一组与参考脚本兼容的指标。

    frames: {canonical: df(open/high/low/close/volume, ret)}
    benchmark: 规范代码，如 000300.SH 或 ^GSPC；若提供且存在，则计算指数相对指标
    返回: {canonical: metrics_dict}
    """
    # 指数用于相对指标
    idx_df: Optional[pd.DataFrame] = None
    if benchmark and benchmark in frames:
        idx_df = frames[benchmark].tail(win_3m)

    out: Dict[str, Dict[str, Any]] = {}
    for sym, df_full in frames.items():
        if sym == benchmark:
            continue
        if df_full is None or df_full.empty or 'ret' not in df_full:
            continue
        df = df_full.tail(win_3m).copy()

        mu = df['ret'].mean()
        sigma = df['ret'].std()
        ann_sigma = float(sigma * math.sqrt(252)) if not np.isnan(sigma) else None
        cumret = float((1 + df['ret'].dropna()).prod() - 1)
        mdd = float(((df['close']/df['close'].cummax())-1).min())
        sharpe = float(((mu * 252 - rf) / (sigma * math.sqrt(252)))) if sigma and sigma > 0 else None

        # 计算其他量化指标
        sortino = None
        calmar = None
        info_ratio = None

        if not df['ret'].empty:
            # 索提诺比率（只考虑下行风险）
            downside_returns = df['ret'][df['ret'] < 0]
            if not downside_returns.empty and downside_returns.std() > 0:
                excess_ret = mu - rf / 252
                sortino = float(excess_ret / downside_returns.std() * math.sqrt(252))

            # 卡尔马比率（年化收益/最大回撤）
            annual_return = mu * 252
            if abs(mdd) > 0.001:  # 避免除零
                calmar = float(annual_return / abs(mdd))

            # 信息比率（如果有基准数据）
            if idx_df is not None and not idx_df.empty:
                idx_returns = idx_df['ret'].reindex(df.index).fillna(0)
                excess_returns = df['ret'] - idx_returns
                if not excess_returns.empty and excess_returns.std() > 0:
                    info_ratio = float(excess_returns.mean() / excess_returns.std() * math.sqrt(252))

        vol_mean = float(df['volume'].mean()) if df['volume'].size else None
        vol_last5 = float(df['volume'].tail(5).mean()) if df['volume'].size else None
        vol_chg = (vol_last5/vol_mean - 1) if vol_mean and vol_mean > 0 else None

        ema20 = df['close'].ewm(span=20, adjust=False).mean()
        ema50 = df['close'].ewm(span=50, adjust=False).mean()
        slope_ema20 = float((ema20.iloc[-1] - ema20.iloc[-5]) / 5)
        slope_ema50 = float((ema50.iloc[-1] - ema50.iloc[-5]) / 5)
        ma_state = 'golden' if ema20.iloc[-1] > ema50.iloc[-1] else 'death'

        rsi_last = float(rsi14(df['close']).iloc[-1])
        macd_val, signal, hist = macd(df['close'])
        macd_last = float(macd_val.iloc[-1])
        hist_last = float(hist.iloc[-1])

        mb = df['close'].rolling(20).mean()
        sd = df['close'].rolling(20).std()
        upper = mb + 2*sd
        lower = mb - 2*sd
        bb_pos = float((df['close'].iloc[-1] - lower.iloc[-1]) / (upper.iloc[-1] - lower.iloc[-1])) if not np.isnan(upper.iloc[-1]) else None

        atr_val = atr14(df)
        supres = support_resistance(df['close'], win_3m)

        # 相对指数
        beta = alpha = corr = ex_ret = None
        if idx_df is not None and 'ret' in df and 'ret' in idx_df:
            ri = df[['ret']].join(idx_df['ret'], how='inner', rsuffix='_idx').dropna()
            if len(ri) > 10 and np.var(ri['ret_idx']) > 0:
                X = ri['ret_idx'].values
                Y = ri['ret'].values
                beta = float(np.cov(X, Y)[0, 1] / np.var(X))
                alpha = float(Y.mean() * 252 - beta * X.mean() * 252)
                corr = float(np.corrcoef(X, Y)[0, 1])
                ex_ret = float((1 + Y).prod() / (1 + X).prod() - 1)

        # 未来20日区间（最近60日均值/波动率）
        base = df_full.copy()
        base['ret'] = base['close'].pct_change()
        recent = base.tail(60)
        mu60 = recent['ret'].mean()
        s60 = recent['ret'].std()
        exp20 = float(mu60 * 20) if not np.isnan(mu60) else None
        s20 = float(s60 * math.sqrt(20)) if not np.isnan(s60) else None
        rng68 = (exp20 - s20, exp20 + s20) if (exp20 is not None and s20 is not None) else None
        rng95 = (exp20 - 2*s20, exp20 + 2*s20) if (exp20 is not None and s20 is not None) else None

        # 计算评分和目标价
        tech_score = calculate_technical_score(rsi_last or 50, hist_last or 0, bb_pos or 0.5, ma_state)
        fund_score = calculate_fund_score(vol_chg or 0, beta or 1.0)
        quant_score = calculate_quant_score(sharpe or 0, mdd or 0, alpha or 0)

        # 获取基本面数据
        fundamentals = get_fundamental_data(sym)
        fundamental_score = calculate_fundamental_score(fundamentals)

        overall_rating = get_overall_rating(tech_score, fund_score, quant_score, fundamental_score)

        # 风险等级评估
        risk_level = "高" if ann_sigma > 0.5 else "中" if ann_sigma > 0.3 else "低"
        position_size = calculate_position_size(overall_rating, risk_level)
        operation_advice = get_operation_advice(overall_rating, rsi_last or 50, vol_chg or 0)

        trend_strength = abs(slope_ema20 or 0) + abs(slope_ema50 or 0)
        target_price = calculate_target_price(float(df['close'].iloc[-1]), supres, trend_strength)

        # 改进止损位计算（基于ATR）
        atr_stop_loss = float(df['close'].iloc[-1]) - (atr_val * 2) if not np.isnan(atr_val) else None

        # 蒙特卡洛模拟预测
        mc_result = None
        enhanced_scenarios = None
        if mu is not None and sigma is not None and not np.isnan(mu) and not np.isnan(sigma):
            mc_result = monte_carlo_simulation(float(df['close'].iloc[-1]), mu, sigma, days=20, simulations=1000)
            enhanced_scenarios = enhanced_scenario_analysis(mc_result, float(df['close'].iloc[-1]))

        out[sym] = {
            'last_close': float(df['close'].iloc[-1]),
            'cumret_3m': float(cumret),
            'mu_d': float(mu) if not np.isnan(mu) else None,
            'sigma_d': float(sigma) if not np.isnan(sigma) else None,
            'ann_sigma': ann_sigma,
            'mdd': mdd,
            'sharpe': sharpe,
            'sortino': sortino,
            'calmar': calmar,
            'info_ratio': info_ratio,
            'vol_mean': vol_mean,
            'vol_last5': vol_last5,
            'vol_chg5_vs_mean': float(vol_chg) if vol_chg is not None else None,
            'ema20_slope_per_day': slope_ema20,
            'ema50_slope_per_day': slope_ema50,
            'ma_state': ma_state,
            'rsi14': rsi_last,
            'macd': macd_last,
            'macd_hist': hist_last,
            'bb_pos_0to1': bb_pos,
            'atr14': float(atr_val) if not np.isnan(atr_val) else None,
            'support_resistance': supres,
            'beta': beta,
            'alpha_ann': alpha,
            'corr_idx': corr,
            'excess_ret_vs_idx': ex_ret,
            'pred_exp20': exp20,
            'pred_68': rng68,
            'pred_95': rng95,
            # 新增字段
            'stock_name': get_stock_name(sym),
            'industry': get_industry(sym),
            'technical_score': tech_score,
            'fund_score': fund_score,
            'quant_score': quant_score,
            'overall_rating': overall_rating,
            'target_price': target_price,
            'atr_stop_loss': atr_stop_loss,
            'position_size': position_size,
            'operation_advice': operation_advice,
            'risk_level': risk_level,
            # 蒙特卡洛模拟结果
            'monte_carlo': mc_result,
            'enhanced_scenarios': enhanced_scenarios,
            # 基本面数据
            'fundamentals': fundamentals,
            'fundamental_score': fundamental_score,
        }
    return out


# ------------------------------ 工具主类 ---------------------------------
@dataclass
class FetchResult:
    frames: Dict[str, pd.DataFrame]
    metrics: Dict[str, Dict[str, Any]]
    errors: List[str]


class StockDataTool:
    """通用股票数据爬取工具类

    - parse_inputs: 从文本/图片/混合解析出规范代码
    - fetch: 通过指定数据源拉取数据
    - compute: 计算指标（兼容参考脚本）
    - to_prompt_payload: 转换为便于提示词使用的结构
    """

    def __init__(self,
                 source: Optional[DataSource] = None,
                 period: str = PERIOD_DEFAULT,
                 interval: str = INTERVAL_DEFAULT,
                 rf: float = RF_DEFAULT,
                 benchmark: Optional[str] = None) -> None:
        self.source = source or YahooFinanceSource()
        self.period = period
        self.interval = interval
        self.rf = rf
        self.benchmark = benchmark  # 推荐: A股用 000300.SH；美股用 ^GSPC

    # -------------------- 输入解析 --------------------
    def parse_inputs(self,
                     codes_text: Optional[str] = None,
                     image_paths: Optional[Iterable[str]] = None) -> List[str]:
        res: List[str] = []
        if codes_text:
            res.extend(OCRCodeExtractor.extract_codes_from_text(codes_text))
        if image_paths:
            ocr = OCRCodeExtractor()
            for p in image_paths:
                try:
                    res.extend(ocr.extract_codes(p))
                except Exception:
                    # 图片解析失败不阻塞流程
                    continue
        # 去重
        out: List[str] = []
        for c in res:
            if c not in out:
                out.append(c)
        return out

    # -------------------- 数据拉取与计算 --------------------
    def fetch(self, canonicals: List[str]) -> FetchResult:
        if not canonicals:
            return FetchResult(frames={}, metrics={}, errors=["未提供任何可解析的股票代码"])
        # 如果设置基准且未包含在列表中，自动补齐，以便计算相对指标
        codes = list(canonicals)
        if self.benchmark and self.benchmark not in codes:
            codes.append(self.benchmark)

        frames, errors = self.source.fetch(codes, self.period, self.interval)
        # 计算指标
        metrics = compute_metrics(frames, benchmark=self.benchmark, rf=self.rf, win_3m=WIN_3M)
        # 仅返回用户请求的标的指标（不包含基准）
        metrics = {k: v for k, v in metrics.items() if k in canonicals}
        # 保留frames也只保留用户标的
        frames = {k: v for k, v in frames.items() if k in canonicals}
        return FetchResult(frames=frames, metrics=metrics, errors=errors)

    # -------------------- 提示词兼容结构 --------------------
    def to_prompt_payload(self, result: FetchResult, mode: str = "quant") -> Dict[str, Any]:
        """将结果转为提示词易用的数据结构
        mode:
          - quant: 面向量化分析师提示词，提供统计与技术指标
          - analyst: 面向股票分析师提示词，突出趋势/支撑阻力/量价
        """
        payload: Dict[str, Any] = {
            'mode': mode,
            'period': self.period,
            'interval': self.interval,
            'as_of': None,
            'errors': result.errors,
            'stocks': []
        }
        # as_of 取任意df的最后日期
        as_of = None
        for df in result.frames.values():
            if not df.empty:
                as_of = df.index.max()
                break
        payload['as_of'] = as_of.isoformat() if as_of is not None and hasattr(as_of, 'isoformat') else str(as_of)

        for code, m in result.metrics.items():
            item: Dict[str, Any] = {
                'code': code,
                'last_close': m.get('last_close'),
                'cumret_3m': m.get('cumret_3m'),
                'ann_sigma': m.get('ann_sigma'),
                'sharpe': m.get('sharpe'),
                'ma_state': m.get('ma_state'),
                'rsi14': m.get('rsi14'),
                'macd_hist': m.get('macd_hist'),
                'bb_pos_0to1': m.get('bb_pos_0to1'),
                'atr14': m.get('atr14'),
                'support_resistance': m.get('support_resistance'),
                'volume': {
                    'mean': m.get('vol_mean'),
                    'last5': m.get('vol_last5'),
                    'chg5_vs_mean': m.get('vol_chg5_vs_mean')
                },
                'relative_to_benchmark': {
                    'beta': m.get('beta'),
                    'alpha_ann': m.get('alpha_ann'),
                    'corr_idx': m.get('corr_idx'),
                    'excess_ret_vs_idx': m.get('excess_ret_vs_idx'),
                },
                'forecast_20d': {
                    'exp': m.get('pred_exp20'),
                    'range68': m.get('pred_68'),
                    'range95': m.get('pred_95')
                }
            }
            if mode == 'analyst':
                # 为股票分析师突出趋势/位置/支撑阻力
                item = {
                    'code': code,
                    'stock_name': m.get('stock_name'),
                    'industry': m.get('industry'),
                    'last_close': m.get('last_close'),
                    'cumret_3m': m.get('cumret_3m'),
                    'trend': {
                        'ma_state': m.get('ma_state'),
                        'ema20_slope_per_day': m.get('ema20_slope_per_day'),
                        'ema50_slope_per_day': m.get('ema50_slope_per_day'),
                    },
                    'momentum': {
                        'rsi14': m.get('rsi14'),
                        'macd': m.get('macd'),
                        'macd_hist': m.get('macd_hist'),
                    },
                    'location': {
                        'bb_pos_0to1': m.get('bb_pos_0to1'),
                        'support_resistance': m.get('support_resistance'),
                    },
                    'risk': {
                        'mdd': m.get('mdd'),
                        'ann_sigma': m.get('ann_sigma'),
                        'atr14': m.get('atr14'),
                    },
                    'volume': {
                        'mean': m.get('vol_mean'),
                        'last5': m.get('vol_last5'),
                        'chg5_vs_mean': m.get('vol_chg5_vs_mean')
                    },
                    'relative_to_benchmark': {
                        'beta': m.get('beta'),
                        'alpha_ann': m.get('alpha_ann'),
                        'corr_idx': m.get('corr_idx'),
                    },
                    'scores': {
                        'technical_score': m.get('technical_score'),
                        'fund_score': m.get('fund_score'),
                        'target_price': m.get('target_price'),
                    }
                }
            payload['stocks'].append(item)
        return payload

    def format_analysis_report(self, result: FetchResult, mode: str = "quant") -> str:
        """生成格式化的分析报告，兼容量化分析师和股票分析师两种模式"""
        if not result.metrics:
            return "无有效数据可分析"

        if mode == "analyst":
            return self._format_analyst_report(result)
        elif mode == "comprehensive":
            return self._format_comprehensive_report(result)
        else:
            return self._format_quant_report(result)

    def _format_quant_report(self, result: FetchResult) -> str:
        """量化分析师格式报告"""
        report = []
        report.append("# 股票量化分析报告")
        report.append(f"数据截止时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}")
        report.append("")

        # 批量摘要表格 - 按照提示词要求的格式
        report.append("## 批量摘要")
        report.append("| 股票代码 | 股票名称 | 3月涨跌 | 技术⭐ | 资金⭐ | 量化⭐ | 综合评级 | 目标价位 | 操作建议 |")
        report.append("|---------|---------|---------|-------|-------|-------|---------|---------|---------|")

        for code, metrics in result.metrics.items():
            name = metrics.get('stock_name', code)
            ret_3m = f"{metrics.get('cumret_3m', 0)*100:+.1f}%"
            tech_score = metrics.get('technical_score', 3)
            fund_score = metrics.get('fund_score', 3)
            quant_score = metrics.get('quant_score', 3)
            overall_rating = metrics.get('overall_rating', '观望')
            target_price = f"{metrics.get('target_price', 0):.2f}"
            operation_advice = metrics.get('operation_advice', '观望')

            report.append(f"| {code} | {name} | {ret_3m} | {tech_score}⭐ | {fund_score}⭐ | {quant_score}⭐ | **{overall_rating}** | {target_price} | {operation_advice} |")

        report.append("")
        return "\n".join(report)

    def _format_comprehensive_report(self, result: FetchResult) -> str:
        """综合量化分析师格式报告 - 完全按照提示词模板"""
        report = []

        # 批量摘要表格
        report.append("## 批量摘要表格")
        report.append("| 股票代码 | 股票名称 | 3月涨跌 | 技术⭐ | 资金⭐ | 量化⭐ | 综合评级 | 目标价位 | 操作建议 |")
        report.append("|---------|---------|---------|-------|-------|-------|---------|---------|---------|")

        for code, metrics in result.metrics.items():
            name = metrics.get('stock_name', code)
            ret_3m = f"{metrics.get('cumret_3m', 0)*100:+.1f}%"
            tech_score = metrics.get('technical_score', 3)
            fund_score = metrics.get('fund_score', 3)
            quant_score = metrics.get('quant_score', 3)
            overall_rating = metrics.get('overall_rating', '观望')
            target_price = f"{metrics.get('target_price', 0):.2f}"
            operation_advice = metrics.get('operation_advice', '观望')

            # 转换星级为星星符号
            tech_stars = "⭐" * tech_score
            fund_stars = "⭐" * fund_score
            quant_stars = "⭐" * quant_score
            report.append(f"| {code} | {name} | {ret_3m} | {tech_stars} | {fund_stars} | {quant_stars} | **{overall_rating}** | {target_price} | {operation_advice} |")

        report.append("")

        # 个股详细分析
        for code, metrics in result.metrics.items():
            name = metrics.get('stock_name', code)
            industry = metrics.get('industry', '未知行业')

            report.append(f"## 📊 **{name}（{code}）- {industry}**")
            report.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            report.append("")

            # 1. 三月量化回顾
            cumret_3m = metrics.get('cumret_3m', 0) * 100
            excess_ret = metrics.get('excess_ret_vs_idx', 0) * 100
            ann_sigma = metrics.get('ann_sigma', 0) * 100
            mdd = metrics.get('mdd', 0) * 100
            sharpe = metrics.get('sharpe', 0)
            beta = metrics.get('beta', 1.0)
            alpha_ann = metrics.get('alpha_ann', 0) * 100
            corr_idx = metrics.get('corr_idx', 0)
            vol_chg = metrics.get('vol_chg5_vs_mean', 0) * 100

            report.append("#### 1. 三月量化回顾")
            report.append(f"- **累计收益**：{cumret_3m:+.1f}% （vs 基准：{excess_ret:+.1f}%）")
            report.append(f"- **风险指标**：波动率{ann_sigma:.1f}% | 最大回撤{mdd:.1f}% | 夏普比率{sharpe:.2f}")
            report.append(f"- **相对强弱**：贝塔{beta:.2f} | 阿尔法{alpha_ann:+.1f}% | 相关性{corr_idx:.2f}")

            # 流动性评级
            if vol_chg > 50:
                liquidity = "活跃"
            elif vol_chg > -20:
                liquidity = "正常"
            else:
                liquidity = "清淡"
            report.append(f"- **成交量变化**：{vol_chg:+.1f}% | 流动性评级：{liquidity}")
            report.append("")

            # 2. 技术指标矩阵
            ma_state = metrics.get('ma_state', 'unknown')
            ema20_slope = metrics.get('ema20_slope_per_day', 0)
            ema50_slope = metrics.get('ema50_slope_per_day', 0)
            rsi14 = metrics.get('rsi14', 50)
            macd = metrics.get('macd', 0)
            macd_hist = metrics.get('macd_hist', 0)
            bb_pos = metrics.get('bb_pos_0to1', 0.5)
            atr14 = metrics.get('atr14', 0)
            support_resistance = metrics.get('support_resistance', {})

            # 趋势状态中文化
            ma_state_cn = {
                'golden': '金叉向上',
                'death': '死叉向下',
                'bullish': '多头排列',
                'bearish': '空头排列',
                'sideways': '横盘整理'
            }.get(ma_state, ma_state)

            # RSI状态判断
            if rsi14 > 80:
                rsi_status = "严重超买"
            elif rsi14 > 70:
                rsi_status = "超买"
            elif rsi14 < 20:
                rsi_status = "严重超卖"
            elif rsi14 < 30:
                rsi_status = "超卖"
            else:
                rsi_status = "正常"

            # 布林带位置判断
            if bb_pos > 1.0:
                bb_status = "突破上轨"
            elif bb_pos > 0.8:
                bb_status = "接近上轨"
            elif bb_pos < 0.2:
                bb_status = "接近下轨"
            elif bb_pos < 0.0:
                bb_status = "跌破下轨"
            else:
                bb_status = "中轨区间"

            report.append("#### 2. 技术指标矩阵")
            report.append(f"- **趋势状态**：{ma_state_cn} | EMA20斜率{ema20_slope:.3f} | EMA50斜率{ema50_slope:.3f}")
            report.append(f"- **动量指标**：RSI{rsi14:.1f}({rsi_status}) | MACD{macd:.3f} | MACD柱{macd_hist:.3f}")
            report.append(f"- **位置指标**：布林带{bb_status}({bb_pos:.2f}) | ATR{atr14:.2f}")

            if support_resistance:
                support = support_resistance.get('q10', 0)
                resistance = support_resistance.get('q90', 0)
                report.append(f"- **关键价位**：支撑{support:.2f} | 阻力{resistance:.2f}")

            # 添加成交量分析
            vol_ratio = metrics.get('vol_ratio_5d', 1.0)
            vol_trend = "放量" if vol_ratio > 1.5 else "缩量" if vol_ratio < 0.7 else "正常"
            report.append(f"- **成交量分析**：5日量比{vol_ratio:.2f}({vol_trend}) | 量价关系{'配合' if vol_ratio > 1.2 and cumret_3m > 0 else '背离'}")

            # 添加更多技术细节
            stoch_k = metrics.get('stoch_k', 50)
            stoch_d = metrics.get('stoch_d', 50)
            williams_r = metrics.get('williams_r', -50)
            cci = metrics.get('cci', 0)

            report.append(f"- **辅助指标**：随机指标K{stoch_k:.1f}/D{stoch_d:.1f} | 威廉指标{williams_r:.1f} | CCI{cci:.1f}")
            report.append("")

            # 3. 综合评分矩阵
            tech_score = metrics.get('technical_score', 3)
            fund_score = metrics.get('fund_score', 3)
            quant_score = metrics.get('quant_score', 3)
            fundamental_score = metrics.get('fundamental_score', 3)
            overall_rating = metrics.get('overall_rating', '观望')

            report.append("#### 3. 综合评分矩阵")
            tech_stars = "⭐" * tech_score
            fund_stars = "⭐" * fund_score
            quant_stars = "⭐" * quant_score
            fundamental_stars = "⭐" * fundamental_score
            report.append(f"- **技术面**：{tech_stars} （趋势+动量+位置综合）")
            report.append(f"- **资金面**：{fund_stars} （成交量+资金流+贝塔综合）")
            report.append(f"- **量化面**：{quant_stars} （收益+风险+相对强弱综合）")
            report.append(f"- **基本面**：{fundamental_stars} （估值+盈利+成长+财务综合）")
            report.append(f"- **综合评级**：**{overall_rating}** （买入/持有/观望/减持/卖出）")
            report.append("")

            # 3.2 深度量化风险分析
            report.append("#### 3.2 深度量化风险分析")

            # VaR和CVaR计算
            var_95 = metrics.get('var_95', 0) * 100
            cvar_95 = metrics.get('cvar_95', 0) * 100
            sortino = metrics.get('sortino_ratio', 0)
            calmar = metrics.get('calmar_ratio', 0)

            report.append(f"- **风险价值**：VaR(95%){var_95:.1f}% | CVaR(95%){cvar_95:.1f}%")
            report.append(f"- **风险调整收益**：索提诺比率{sortino:.2f} | 卡尔马比率{calmar:.2f}")

            # 波动率分解
            systematic_risk = (beta ** 2) * (metrics.get('market_variance', 0.04))
            idiosyncratic_risk = (ann_sigma/100) ** 2 - systematic_risk
            systematic_pct = systematic_risk / ((ann_sigma/100) ** 2) * 100 if ann_sigma > 0 else 0

            report.append(f"- **风险分解**：系统性风险{systematic_pct:.1f}% | 特异性风险{100-systematic_pct:.1f}%")

            # 流动性风险
            avg_volume = metrics.get('avg_volume_3m', 0)
            volume_volatility = metrics.get('volume_volatility', 0) * 100
            bid_ask_spread = metrics.get('bid_ask_spread', 0) * 100

            if avg_volume > 0:
                report.append(f"- **流动性风险**：日均成交量{avg_volume/10000:.1f}万手 | 成交量波动率{volume_volatility:.1f}%")

            report.append("")

            # 3.1 基本面详细分析
            fundamentals = metrics.get('fundamentals', {})
            if fundamentals:
                report.append("#### 3.1 基本面分析详情")

                # 估值指标
                pe = fundamentals.get('pe_ratio')
                pb = fundamentals.get('pb_ratio')
                ps = fundamentals.get('price_to_sales')
                peg = fundamentals.get('peg_ratio')

                valuation_items = []
                if pe: valuation_items.append(f"PE {pe:.1f}")
                if pb: valuation_items.append(f"PB {pb:.2f}")
                if ps: valuation_items.append(f"PS {ps:.2f}")
                if peg: valuation_items.append(f"PEG {peg:.2f}")

                if valuation_items:
                    report.append(f"- **估值指标**：{' | '.join(valuation_items)}")

                # 盈利能力
                roe = fundamentals.get('roe')
                roa = fundamentals.get('roa')
                profit_margin = fundamentals.get('profit_margin')
                operating_margin = fundamentals.get('operating_margin')

                profitability_items = []
                if roe: profitability_items.append(f"ROE {roe*100:.1f}%")
                if roa: profitability_items.append(f"ROA {roa*100:.1f}%")
                if profit_margin: profitability_items.append(f"净利率 {profit_margin*100:.1f}%")
                if operating_margin: profitability_items.append(f"营业利率 {operating_margin*100:.1f}%")

                if profitability_items:
                    report.append(f"- **盈利能力**：{' | '.join(profitability_items)}")

                # 成长性
                revenue_growth = fundamentals.get('revenue_growth')
                earnings_growth = fundamentals.get('earnings_growth')

                growth_items = []
                if revenue_growth: growth_items.append(f"营收增长 {revenue_growth*100:+.1f}%")
                if earnings_growth: growth_items.append(f"利润增长 {earnings_growth*100:+.1f}%")

                if growth_items:
                    report.append(f"- **成长性**：{' | '.join(growth_items)}")

                # 财务健康
                debt_to_equity = fundamentals.get('debt_to_equity')
                current_ratio = fundamentals.get('current_ratio')

                financial_items = []
                if debt_to_equity: financial_items.append(f"资产负债率 {debt_to_equity:.2f}")
                if current_ratio: financial_items.append(f"流动比率 {current_ratio:.2f}")

                if financial_items:
                    report.append(f"- **财务健康**：{' | '.join(financial_items)}")

                report.append("")

            # 4. 一月预测与情景
            forecast_exp = metrics.get('pred_exp20', 0) * 100
            forecast_68 = metrics.get('pred_68', (0, 0))
            target_price = metrics.get('target_price', 0)
            mc_result = metrics.get('monte_carlo', {})
            enhanced_scenarios = metrics.get('enhanced_scenarios', {})

            report.append("#### 4. 一月预测与情景")
            report.append(f"- **期望收益**：{forecast_exp:+.1f}% （置信区间：{forecast_68[0]*100:+.1f}% ~ {forecast_68[1]*100:+.1f}%）")
            report.append(f"- **目标价位**：{target_price:.2f}元 （基于技术+量化模型）")

            # 使用蒙特卡洛模拟结果进行概率评估
            if enhanced_scenarios:
                win_prob = enhanced_scenarios.get('win_probability', 50)
                report.append(f"- **胜率评估**：{win_prob:.1f}% （基于1000次蒙特卡洛模拟）")
                report.append("- **概率评估**：")

                pessimistic = enhanced_scenarios.get('pessimistic', {})
                conservative = enhanced_scenarios.get('conservative', {})
                base = enhanced_scenarios.get('base', {})
                optimistic = enhanced_scenarios.get('optimistic', {})

                if pessimistic:
                    report.append(f"  - 悲观情景（{pessimistic.get('probability', 20)}%）：{pessimistic.get('description', '下跌风险')}")
                if conservative:
                    report.append(f"  - 保守情景（{conservative.get('probability', 30)}%）：{conservative.get('description', '谨慎整理')}")
                if base:
                    report.append(f"  - 基准情景（{base.get('probability', 30)}%）：{base.get('description', '区间震荡')}")
                if optimistic:
                    report.append(f"  - 乐观情景（{optimistic.get('probability', 20)}%）：{optimistic.get('description', '突破上涨')}")
            else:
                # 回退到简单预测
                optimistic = forecast_exp * 1.5
                base = forecast_exp
                pessimistic = forecast_exp * 0.5

                report.append("- **概率评估**：")
                report.append(f"  - 乐观情景（30%）：突破阻力位，涨幅{optimistic:+.1f}%")
                report.append(f"  - 基准情景（50%）：区间震荡，涨幅{base:+.1f}%")
                report.append(f"  - 悲观情景（20%）：跌破支撑，跌幅{pessimistic:+.1f}%")

            # 蒙特卡洛分位数信息
            if mc_result:
                p5 = (mc_result.get('p5', 0) / metrics.get('last_close', 1) - 1) * 100
                p95 = (mc_result.get('p95', 0) / metrics.get('last_close', 1) - 1) * 100
                report.append(f"- **极端情景**：P5={p5:+.1f}% | P95={p95:+.1f}% （蒙特卡洛模拟）")

            report.append("")

            # 5. 操作建议与风控
            operation_advice = metrics.get('operation_advice', '观望')
            atr_stop_loss = metrics.get('atr_stop_loss', 0)
            position_size = metrics.get('position_size', '0%')

            report.append("#### 5. 操作建议与风控")
            report.append(f"- **操作建议**：**{operation_advice}** （基于综合评分）")

            # 建仓策略
            if overall_rating == "买入":
                strategy = "分批建仓，分3次完成"
            elif overall_rating == "持有":
                strategy = "持有为主，可适度加仓"
            else:
                strategy = "观望等待"

            report.append(f"- **建仓策略**：{strategy}")
            report.append(f"- **止损位**：{atr_stop_loss:.2f}元 （基于ATR计算）")

            if support_resistance:
                take_profit = support_resistance.get('q90', target_price) * 1.05
                report.append(f"- **止盈位**：{take_profit:.2f}元 （基于阻力位或目标价）")

            report.append(f"- **仓位建议**：{position_size} （基于风险评级）")
            report.append("")

            # 6. 风险提示与催化因素
            risk_level = metrics.get('risk_level', '中')

            report.append("#### 6. 风险提示与催化因素")
            report.append(f"- **主要风险**：波动率{ann_sigma:.1f}%，风险等级{risk_level} （技术/基本面/市场/流动性）")
            report.append("- **催化因素**：财报发布、政策变化、行业动态 （财报/政策/行业/事件）")
            report.append("- **关注指标**：RSI回调、成交量持续性、支撑位测试 （需重点监控的技术指标）")
            report.append("")
            report.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            report.append("")

        # 多股票行业对比分析
        if len(result.metrics) > 1:
            industry_analysis = industry_comparison_analysis(result.metrics)

            if industry_analysis.get('rankings'):
                report.append("## 🏭 **行业对比分析**")
                report.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                report.append("")

                for industry, rankings in industry_analysis['rankings'].items():
                    if len(rankings['returns']) > 1:
                        report.append(f"### {industry}板块内部对比")

                        # 收益率排名
                        report.append("#### 📈 收益率排名")
                        for i, (code, ret) in enumerate(rankings['returns'], 1):
                            name = result.metrics[code].get('stock_name', code)
                            report.append(f"{i}. **{name}({code})**：{ret}")
                        report.append("")

                        # 风险调整收益排名
                        report.append("#### ⚖️ 风险调整收益排名（夏普比率）")
                        for i, (code, sharpe) in enumerate(rankings['sharpe'], 1):
                            name = result.metrics[code].get('stock_name', code)
                            report.append(f"{i}. **{name}({code})**：{sharpe}")
                        report.append("")

                        # 估值对比
                        if rankings['valuation']:
                            report.append("#### 💰 估值对比")
                            for i, (code, pe) in enumerate(rankings['valuation'], 1):
                                name = result.metrics[code].get('stock_name', code)
                                report.append(f"{i}. **{name}({code})**：{pe}")
                            report.append("")

                        # 基本面综合排名
                        report.append("#### 🏢 基本面综合排名")
                        for i, (code, fund_score) in enumerate(rankings['fundamentals'], 1):
                            name = result.metrics[code].get('stock_name', code)
                            report.append(f"{i}. **{name}({code})**：{fund_score}")
                        report.append("")

                # 投资配置建议
                report.append("### 🎯 板块配置建议")

                # 找出最佳配置组合
                best_stocks = []
                for code, metrics in result.metrics.items():
                    total_score = (metrics.get('technical_score', 3) +
                                 metrics.get('fund_score', 3) +
                                 metrics.get('quant_score', 3) +
                                 metrics.get('fundamental_score', 3))
                    best_stocks.append((code, total_score, metrics))

                best_stocks.sort(key=lambda x: x[1], reverse=True)

                report.append("- **推荐配置**：")
                total_weight = 0
                for i, (code, score, metrics) in enumerate(best_stocks[:3], 1):
                    name = metrics.get('stock_name', code)
                    rating = metrics.get('overall_rating', '观望')

                    if rating == "买入":
                        weight = 40 if i == 1 else 30 if i == 2 else 20
                    elif rating == "持有":
                        weight = 30 if i == 1 else 25 if i == 2 else 15
                    else:
                        weight = 10

                    total_weight += weight
                    report.append(f"  {i}. **{name}({code})**：{weight}% （评分{score}/20，{rating}）")

                report.append(f"- **总仓位建议**：{min(total_weight, 100)}%")
                report.append("")

                # 风险分散建议
                correlations = []
                codes = list(result.metrics.keys())
                for i in range(len(codes)):
                    for j in range(i+1, len(codes)):
                        corr = result.metrics[codes[i]].get('corr_idx', 0)
                        correlations.append(abs(corr))

                avg_corr = np.mean(correlations) if correlations else 0

                if avg_corr > 0.8:
                    diversification = "相关性较高，分散效果有限"
                elif avg_corr > 0.5:
                    diversification = "相关性中等，适度分散风险"
                else:
                    diversification = "相关性较低，分散效果良好"

                report.append(f"- **分散化效果**：{diversification} （平均相关性{avg_corr:.2f}）")
                report.append("")

        return "\n".join(report)

    def _format_analyst_report(self, result: FetchResult) -> str:
        """股票分析师格式报告"""
        report = []

        for code, metrics in result.metrics.items():
            name = metrics.get('stock_name', code)
            industry = metrics.get('industry', '未分类')

            report.append(f"[股票] {name}（{code}）- {industry}")
            report.append("=" * 40)
            report.append("")

            # 1. 三月回顾
            cumret_3m = metrics.get('cumret_3m', 0) * 100
            ma_state = metrics.get('ma_state', 'unknown')
            vol_chg = metrics.get('vol_chg5_vs_mean', 0)

            trend_desc = "强势上涨" if ma_state == 'golden' and cumret_3m > 20 else \
                        "温和上涨" if ma_state == 'golden' else \
                        "震荡下跌" if ma_state == 'death' else "横盘整理"

            vol_desc = "明显放量" if vol_chg > 0.5 else "适度放量" if vol_chg > 0.2 else \
                      "缩量" if vol_chg < -0.2 else "正常"

            report.append("1. 三月回顾（简明扼要）")
            report.append(f"   • 涨跌幅：{cumret_3m:.1f}%")
            report.append(f"   • 主要趋势：{trend_desc}")
            report.append(f"   • 关键事件：[需结合行业和近期新闻]")
            report.append(f"   • 成交量：{vol_desc}")
            report.append("")

            # 2. 技术指标
            rsi = metrics.get('rsi14', 50)
            bb_pos = metrics.get('bb_pos_0to1', 0.5)
            sr = metrics.get('support_resistance', {})

            position_desc = "超买区域" if rsi > 70 else "超卖区域" if rsi < 30 else "正常区域"
            if bb_pos > 1.0:
                position_desc += "，突破上轨"
            elif bb_pos < 0.0:
                position_desc += "，跌破下轨"

            report.append("2. 技术指标")
            report.append(f"   • 当前位置：{position_desc}")
            report.append(f"   • 均线状态：{'多头排列' if ma_state == 'golden' else '空头排列'}")
            report.append(f"   • 关键支撑：{sr.get('q10', 0):.2f}元")
            report.append(f"   • 关键压力：{sr.get('q90', 0):.2f}元")
            report.append("")

            # 3. 综合评分
            tech_score = metrics.get('technical_score', 3)
            fund_score = metrics.get('fund_score', 3)

            report.append("3. 综合评分")
            report.append(f"   • 技术面：{tech_score}分（自动计算）")
            report.append(f"   • 资金面：{fund_score}分（自动计算）")
            report.append(f"   • 消息面：待评估（需人工评估）")
            report.append("")

            # 4. 一月预测
            target_price = metrics.get('target_price', metrics.get('last_close', 0))
            pred_exp = metrics.get('pred_exp20', 0) * 100

            trend_judge = "看涨" if tech_score >= 4 and fund_score >= 4 else \
                         "看跌" if tech_score <= 2 and fund_score <= 2 else "震荡"

            prob_desc = "高概率" if (tech_score + fund_score) >= 8 else \
                       "中等概率" if (tech_score + fund_score) >= 6 else "低概率"

            operation = "可适量关注" if trend_judge == "看涨" else \
                       "建议观望" if trend_judge == "震荡" else "谨慎回避"

            report.append("4. 一月预测")
            report.append(f"   • 趋势判断：{trend_judge}")
            report.append(f"   • 目标价位：{target_price:.2f}元（算法计算）")
            report.append(f"   • 概率评估：{prob_desc}")
            report.append(f"   • 操作建议：{operation}")
            report.append("")

            # 5. 风险提示
            mdd = metrics.get('mdd', 0) * 100
            ann_sigma = metrics.get('ann_sigma', 0) * 100
            stop_loss = sr.get('swing_low', metrics.get('last_close', 0)) * 0.95

            report.append("5. 风险提示")
            report.append(f"   • 主要风险点：最大回撤{mdd:.1f}%，年化波动{ann_sigma:.1f}%")
            report.append(f"   • 止损位建议：{stop_loss:.2f}元")
            report.append("")
            report.append("=" * 40)
            report.append("")

        return "\n".join(report)

    def validate_realtime_data(self, ocr_info: dict, api_metrics: dict) -> dict:
        """验证OCR提取的实时数据与API数据的一致性"""
        validation = {
            'price_diff_pct': 0,
            'data_freshness': 'unknown',
            'anomaly_confirmed': False,
            'warnings': []
        }

        if not ocr_info.get('prices') or not api_metrics:
            validation['warnings'].append("缺少对比数据")
            return validation

        # 尝试匹配OCR价格与API价格
        try:
            ocr_prices = [float(p) for p in ocr_info['prices'] if p.replace('.', '').isdigit()]
            if ocr_prices and api_metrics:
                # 取最接近的价格进行对比
                api_price = list(api_metrics.values())[0].get('last_close', 0)
                closest_ocr_price = min(ocr_prices, key=lambda x: abs(x - api_price))

                price_diff = abs(closest_ocr_price - api_price) / api_price * 100
                validation['price_diff_pct'] = price_diff

                if price_diff < 1:
                    validation['data_freshness'] = 'very_fresh'
                elif price_diff < 3:
                    validation['data_freshness'] = 'fresh'
                else:
                    validation['data_freshness'] = 'stale'
                    validation['warnings'].append(f"价格差异较大: {price_diff:.1f}%")

        except Exception as e:
            validation['warnings'].append(f"价格验证失败: {str(e)}")

        # 验证异常涨跌
        if ocr_info.get('anomaly_detected'):
            validation['anomaly_confirmed'] = True
            validation['warnings'].append("检测到异常涨跌，建议重点分析")

        return validation

    def get_industry_peers(self, code: str, limit: int = 5) -> List[str]:
        """获取同行业股票列表"""
        industry = get_industry(code)
        peers = [k for k, v in INDUSTRY_MAP.items() if v == industry and k != code]
        return peers[:limit]

    def enhanced_parse_inputs(self,
                            codes_text: Optional[str] = None,
                            image_paths: Optional[Iterable[str]] = None) -> Tuple[List[str], dict]:
        """增强版输入解析，返回代码列表和OCR信息"""
        codes = self.parse_inputs(codes_text, image_paths)
        ocr_info = {}

        if image_paths:
            ocr = OCRCodeExtractor()
            for path in image_paths:
                try:
                    info = ocr.extract_full_info(path)
                    ocr_info.update(info)
                except Exception as e:
                    ocr_info['errors'] = ocr_info.get('errors', []) + [str(e)]

        return codes, ocr_info


# ------------------------------ 使用示例 ---------------------------------
EXAMPLE = r"""
# 1) 文本/图片解析股票代码
from scripts.stock_data_tool import StockDataTool

# A股: 600522, 深市: 000001, 港股: 0700.HK 或 700, 美股: TSLA, 指数: ^GSPC
text = "我想看看 600522, 601869.SH, TSLA, 700, ^GSPC 的数据"
images = ["./screenshots/sample1.png"]  # 可选，如无pytesseract可忽略

tool = StockDataTool(benchmark="000300.SH")  # A股相对沪深300
codes = tool.parse_inputs(codes_text=text, image_paths=None)

# 2) 拉取数据 + 计算指标
result = tool.fetch(codes)

# 3) 转换为提示词友好结构（量化/分析师两种模式）
quant_payload = tool.to_prompt_payload(result, mode="quant")
analyst_payload = tool.to_prompt_payload(result, mode="analyst")

# 4) 序列化输出
print(json.dumps(quant_payload, ensure_ascii=False, indent=2))
"""

if __name__ == "__main__":  # 简易CLI用法
    demo_text = "研究 600522, 601869.SH 与 TSLA 以及 0700.HK"
    tool = StockDataTool(benchmark="000300.SH")
    codes = tool.parse_inputs(codes_text=demo_text)
    res = tool.fetch(codes)
    payload = tool.to_prompt_payload(res, mode="quant")
    print(json.dumps(payload, ensure_ascii=False, indent=2))

